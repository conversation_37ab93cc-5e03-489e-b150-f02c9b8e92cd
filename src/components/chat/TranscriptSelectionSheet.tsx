'use client';

import { useChat } from '@/contexts/ChatContext';
import { useTranscripts } from '@/hooks/use-transcripts';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Sheet, Sheet<PERSON>ontent, SheetHeader, SheetTitle, SheetDescription, SheetFooter } from '@/components/ui/sheet';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Transcript } from '@/types';
import { useRouter } from 'next/navigation';

interface TranscriptSelectionSheetProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
}

export function TranscriptSelectionSheet({ isOpen, onOpenChange }: TranscriptSelectionSheetProps) {
  const { transcripts } = useTranscripts();
  const { selectedTranscriptIds, addTranscript, removeTranscript } = useChat();
  const router = useRouter();

  const handleTranscriptSelect = (checked: boolean, transcriptId: string) => {
    if (checked) {
      addTranscript(transcriptId);
    } else {
      removeTranscript(transcriptId);
    }
  };

  return (
    <Sheet open={isOpen} onOpenChange={onOpenChange}>
      <SheetContent className="w-full sm:w-[540px] flex flex-col">
        <SheetHeader>
          <SheetTitle>Attach Transcripts</SheetTitle>
          <SheetDescription>
            Select call transcripts to provide additional context to the conversation.
          </SheetDescription>
        </SheetHeader>
        <ScrollArea className="flex-grow my-4 mx-4">
          <div className="space-y-4 pr-6">
            {transcripts.length > 0 ? (
              transcripts.map((transcript: Transcript) => (
                <div key={transcript.id} className="flex items-start space-x-4 p-4 rounded-lg border">
                  <Checkbox
                    id={`transcript-${transcript.id}`}
                    checked={selectedTranscriptIds.includes(transcript.id)}
                    onCheckedChange={(checked) => handleTranscriptSelect(!!checked, transcript.id)}
                    className="mt-1"
                  />
                  <label htmlFor={`transcript-${transcript.id}`} className="flex-grow cursor-pointer">
                    <p className="font-medium text-sm text-zinc-800 dark:text-zinc-100">{new Date(transcript.startedAt).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                    })} at {new Date(transcript.startedAt).toLocaleTimeString('en-US', {
                      hour: '2-digit',
                      minute: '2-digit',
                    })}</p>
                    <p className="text-sm text-zinc-800 dark:text-zinc-100">{transcript.summary}</p>
                  </label>
                </div>
              ))
            ) : (
              <p className="text-muted-foreground text-center py-8">No transcripts available.</p>
            )}
          </div>
        </ScrollArea>
        <SheetFooter>
          <Button variant="outline" onClick={() => router.push('/transcripts')}>View Transcripts Details</Button>
          <Button onClick={() => onOpenChange(false)}>Done</Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
} 