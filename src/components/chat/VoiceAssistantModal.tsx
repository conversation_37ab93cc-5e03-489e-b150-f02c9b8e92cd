import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import Vapi from "@vapi-ai/web";
import { useEffect } from 'react';
import { toast } from 'sonner';
import profile from '@/app/data/profile.json';

interface VoiceAssistantModalProps {
  isOpen: boolean;
  onClose: () => void;
  formattedMessages: { role: string; content: string }[];
  isSessionActive: boolean;
  onSessionStateChange: (active: boolean) => void;
}

const vapi = new Vapi(process.env.NEXT_PUBLIC_VAPI_SDK_API_KEY!);

export function VoiceAssistantModal({ isOpen, onClose, formattedMessages, isSessionActive, onSessionStateChange }: VoiceAssistantModalProps) {

  useEffect(() => {
    if (!process.env.NEXT_PUBLIC_VAPI_SDK_API_KEY) {
      console.error("Vapi Public Key is not set.");
      toast.error("Vapi Public Key is not set. Please check your environment variables.");
      return;
    }

    vapi.on("call-start", () => {
      onSessionStateChange(true);
      toast.success("Voice session started!");
      onClose(); // Close modal after session starts
    });

    vapi.on("call-end", () => {
      onSessionStateChange(false);
      toast.info("Voice session ended.");
    });

    vapi.on("error", (e) => {
      console.error("Vapi error:", e);
      toast.error(`Vapi error: ${e.message}`);
      onSessionStateChange(false);
    });

    return () => {
      vapi.removeAllListeners();
    };
  }, [onClose, onSessionStateChange]);

  const startSession = async () => {
    try {
      const systemPromptContent = `You are a helpful voice assistant. Here is the current conversation context: ${JSON.stringify(formattedMessages)}. Always be very concise and to the point. 
      
      [Identity]  
You are Liz, a friendly and knowledgeable assistant for a health insurance company, focusing on assisting members with their insurance plan inquiries. The member's profile is: ${JSON.stringify(profile.profile)}. There is valuable information in the member's profile that you can use to help the user.

[Style]  
- Maintain a warm, approachable tone, allowing members to feel comfortable and understood.  
- Use clear, concise language while ensuring empathy and professionalism.  

[Response Guidelines]  
- Provide concise and direct answers to member inquiries using realistic mock data.  
- Initiate conversations by greeting the caller warmly. Avoid mentioning the testing context. 

[Task & Goals]  
1. Greet the caller personally and pleasantly as the call begins.
2. Answer any insurance plan questions using generated mock data, ensuring clarity and relevance to the user's inquiry. Insurance questions should be responded to as if you already have all of their data, eliminating the need to ask them for login or account information.
3. You can perform all functions asked of you related to the member's profile and insurance plan. You will simulate performing these actions, but will never mention that you are simulating them.

[Error Handling / Fallback]  
- If a user's input is unclear, ask polite clarifying questions to ensure understanding.  
- If the conversation veers off-topic, respectfully guide it back to relevant questions about the insurance plan.  
- Apologize gracefully if a misunderstanding occurs and offer additional assistance. `;

      await vapi.start({
        transcriber: {
          provider: "deepgram",
          model: "nova-2",
          language: "en-US",
        },
        credentials: [
          {
              provider: "11labs",
              apiKey: "sk_001adc48029272cecf059243ea02219014649b8ddb8de2f8"
          }
      ],
      backgroundSound: "office",
        model: {
          provider: "openai",
          model: "gpt-4o-mini",
          messages: [
            {
              role: "system",
              content: systemPromptContent,
            },
          ],
        },
        voice: {
          provider: "11labs",
          voiceId: "M6N6IdXhi5YNZyZSDe7k",
      },
      name: "My Inline Assistant",
      firstMessage: "Hey there. My name is Liz. How can I help you today? I can help you with a previous topic or we can start a new one.",
      firstMessageMode: "assistant-speaks-first",
      });
    } catch (error) {
      console.error("Failed to start Vapi session:", error);
      toast.error("Failed to start voice session.");
      onSessionStateChange(false);
    }
  };

  const stopSession = () => {
    vapi.stop();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Voice Assistant</DialogTitle>
          <DialogDescription>
            {isSessionActive
              ? "Voice session is active. Click 'Stop Session' to end."
              : "Start a voice session to talk with the assistant. Please note that you will need to grant microphone access when you start the session."}
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button type="button" onClick={isSessionActive ? stopSession : startSession}>
            {isSessionActive ? "Stop Session" : "Start Session"}
          </Button>
          <Button type="button" variant="outline" onClick={onClose} disabled={isSessionActive}>
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}