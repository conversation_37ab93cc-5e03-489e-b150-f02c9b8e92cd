import { Thread, Message, Transcript, ChatResponsePart } from '@/types';

// Helper function to generate unique IDs
const generateId = () => Math.random().toString(36).substr(2, 9);

// Helper function to get current timestamp
const getCurrentTimestamp = () => Date.now();

// Mock chat conversation data
export const createMockChatThread = (): Thread => {
  const threadId = generateId();
  const baseTimestamp = getCurrentTimestamp() - (2 * 60 * 60 * 1000); // 2 hours ago
  
  const messages: Message[] = [
    {
      id: generateId(),
      sender: 'user',
      timestamp: baseTimestamp,
      content: "Hi, I've been having some stomach issues lately and I'm worried it might be related to my <PERSON><PERSON><PERSON>'s disease. Can you help me understand what I should do?"
    },
    {
      id: generateId(),
      sender: 'ai',
      timestamp: baseTimestamp + (2 * 60 * 1000), // 2 minutes later
      content: [
        {
          type: 'text',
          content: "I understand your concern about your stomach issues, especially with your <PERSON><PERSON><PERSON>'s disease history. Let me help you with some guidance and next steps."
        },
        {
          type: 'text',
          content: "First, it's important to monitor your symptoms. Are you experiencing any of the following: severe abdominal pain, persistent diarrhea, blood in stool, fever, or significant weight loss?"
        }
      ] as ChatResponsePart[]
    },
    {
      id: generateId(),
      sender: 'user',
      timestamp: baseTimestamp + (5 * 60 * 1000), // 5 minutes later
      content: "Yes, I've had some abdominal pain and diarrhea for the past few days. No blood or fever though."
    },
    {
      id: generateId(),
      sender: 'ai',
      timestamp: baseTimestamp + (7 * 60 * 1000), // 7 minutes later
      content: [
        {
          type: 'text',
          content: "Thank you for that information. Given your symptoms and Crohn's disease history, I'd recommend contacting your gastroenterologist soon. In the meantime, here are some things that might help:"
        },
        {
          type: 'text',
          content: "• Stay hydrated with clear fluids\n• Consider a bland diet (BRAT diet: bananas, rice, applesauce, toast)\n• Avoid dairy, high-fiber foods, and spicy foods temporarily\n• Monitor your symptoms closely"
        },
        {
          type: 'text',
          content: "Would you like me to help you schedule an appointment with your gastroenterologist or provide more information about managing Crohn's flare-ups?"
        }
      ] as ChatResponsePart[]
    },
    {
      id: generateId(),
      sender: 'user',
      timestamp: baseTimestamp + (10 * 60 * 1000), // 10 minutes later
      content: "Yes, please help me schedule an appointment. I think I should see Dr. Martinez as soon as possible."
    },
    {
      id: generateId(),
      sender: 'ai',
      timestamp: baseTimestamp + (12 * 60 * 1000), // 12 minutes later
      content: [
        {
          type: 'text',
          content: "I'll help you get in touch with Dr. Martinez's office. Let me check their availability and provide you with the contact information."
        },
        {
          type: 'text',
          content: "Dr. Martinez's office: (555) 123-4567\nAddress: 123 Medical Center Dr, Suite 200\n\nI recommend calling them first thing in the morning to explain your symptoms and request an urgent appointment. If you can't reach them or need immediate care, consider visiting an urgent care center."
        }
      ] as ChatResponsePart[]
    }
  ];

  return {
    id: threadId,
    name: "Crohn's Symptom Consultation",
    messages,
    createdAt: baseTimestamp,
    lastModifiedAt: baseTimestamp + (12 * 60 * 1000),
    initialMessageSent: true
  };
};

// Mock transcript data - First call (before the chat)
export const createMockTranscript1 = (): Transcript => {
  const baseTimestamp = getCurrentTimestamp() - (3 * 60 * 60 * 1000); // 3 hours ago
  
  return {
    id: generateId(),
    summary: "Patient called with initial concerns about stomach discomfort and potential Crohn's flare-up. Scheduled for follow-up consultation and advised to monitor symptoms.",
    transcript: `AI: Hello, this is your Virtual Health Assistant. How can I help you today?

User: Hi, I've been having some stomach discomfort for the past couple of days and I'm a bit worried since I have Crohn's disease.

AI: I understand your concern. Stomach discomfort can be particularly worrying when you have Crohn's disease. Can you describe the type of discomfort you're experiencing? Is it cramping, sharp pain, or something else?

User: It's mostly cramping, and I've had some loose stools too. Not as bad as during my last flare-up, but I'm concerned it might be starting again.

AI: Thank you for that information. It's good that you're monitoring your symptoms closely. Have you noticed any other symptoms like fever, blood in your stool, or significant changes in your appetite?

User: No fever or blood, but my appetite has been a bit off. I'm just not as hungry as usual.

AI: I appreciate you sharing these details. Given your history with Crohn's disease and these symptoms, it would be wise to follow up with your gastroenterologist. In the meantime, you might want to stick to easily digestible foods and stay well hydrated.

User: Should I be worried? Do I need to see someone right away?

AI: While your symptoms don't sound like an emergency, it's always better to be proactive with Crohn's disease. I'd recommend contacting your gastroenterologist within the next day or two. If your symptoms worsen significantly, don't hesitate to seek immediate medical attention.

User: Okay, that makes sense. I'll call Dr. Martinez's office tomorrow morning.

AI: That sounds like a good plan. Is there anything else I can help you with today regarding your symptoms or Crohn's management?

User: No, I think that covers it. Thank you for the advice.

AI: You're welcome. Take care, and don't hesitate to call back if you have any other concerns.`,
    startedAt: new Date(baseTimestamp).toISOString(),
    endedAt: new Date(baseTimestamp + (8 * 60 * 1000)).toISOString(), // 8 minute call
    endedReason: "customer-ended-call",
    recordingUrls: {
      mono: "https://example.com/mock-recording-1-mono.wav",
      stereo: "https://example.com/mock-recording-1-stereo.wav"
    }
  };
};

// Mock transcript data - Second call (after the chat)
export const createMockTranscript2 = (): Transcript => {
  const baseTimestamp = getCurrentTimestamp() - (30 * 60 * 1000); // 30 minutes ago
  
  return {
    id: generateId(),
    summary: "Follow-up call after chat consultation. Patient confirmed appointment scheduling with Dr. Martinez and received additional guidance on symptom management and when to seek urgent care.",
    transcript: `AI: Hello again! I see you had a chat consultation with us earlier about your Crohn's symptoms. How are you feeling now?

User: Hi, yes I did. I'm feeling about the same, maybe slightly better after following some of the dietary suggestions from our chat.

AI: That's good to hear. I see from our previous conversation that you were planning to call Dr. Martinez's office. Were you able to get in touch with them?

User: Yes, I called them right after our chat. They were able to schedule me for an appointment tomorrow afternoon, which is great.

AI: Excellent! That's exactly what we hoped for. How are you managing your symptoms in the meantime? Are you staying hydrated and following the dietary recommendations?

User: Yes, I've been drinking plenty of water and sticking to bland foods like you suggested. The cramping seems a bit less intense today.

AI: That's encouraging. It sounds like you're taking all the right steps. Since you have an appointment tomorrow, make sure to keep track of any changes in your symptoms so you can give Dr. Martinez a complete picture.

User: Good idea. Should I be concerned if the symptoms get worse before my appointment?

AI: If you experience severe pain, high fever, vomiting, or any blood in your stool, you should seek immediate medical attention rather than waiting for your appointment. Otherwise, the mild to moderate symptoms you're describing can typically wait for your scheduled visit.

User: That's reassuring. I feel much better having a plan in place.

AI: I'm glad we could help you feel more confident about managing this situation. Is there anything else you'd like to discuss before your appointment with Dr. Martinez?

User: No, I think I'm all set. Thank you for all the help today, both in the chat and now on this call.

AI: You're very welcome! Take care, and I hope your appointment goes well tomorrow.`,
    startedAt: new Date(baseTimestamp).toISOString(),
    endedAt: new Date(baseTimestamp + (6 * 60 * 1000)).toISOString(), // 6 minute call
    endedReason: "customer-ended-call",
    recordingUrls: {
      mono: "https://example.com/mock-recording-2-mono.wav",
      stereo: "https://example.com/mock-recording-2-stereo.wav"
    }
  };
};
